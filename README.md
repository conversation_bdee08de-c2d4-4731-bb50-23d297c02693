# WebNav Hub - 粘土拟物化导航中心

灵感来自高端机械键盘的粘土拟物化导航中心。柔和3D圆角按钮，灰白色背景，焦橙色点缀，几何无衬线字体。

## 设计风格

网站采用 **Claymorphism (粘土拟物化)** 风格，通过柔和的阴影效果，创造出一种像粘土一样柔软、立体的感觉，完美复刻了键盘按键的圆润和厚实质感。

主要设计元素包括：

- **简洁的 3D 圆角按钮**: 所有交互元素都具有圆润的边缘和立体感。
- **灰白色背景**: 整体背景采用干净、柔和的灰白色调。
- **中性灰色调色板**: 以浅灰色和骨白色为主色调，辅以焦橙色作为强调色。
- **几何无衬线字体**: 使用 Inter 字体，现代且易读。

## 功能

- **分类导航**: 将链接按类别分组，方便快速查找。
- **平滑滚动**: 点击导航链接时，页面会平滑滚动到对应分类。
- **返回顶部**: 滚动300px后显示粘土拟物化圆形按钮，一键返回顶部。
- **键盘友好**: 支持键盘导航和操作，包括返回顶部按钮。
- **响应式设计**: 适配不同屏幕尺寸，针对移动端优化。
- **高性能优化**: 专为流畅运行而设计，自动适配低端设备。

## 技术栈

- HTML5
- CSS3 (自定义属性, Grid, Flexbox, 动画)
- JavaScript (DOM 操作, 事件监听)
- Google Fonts (Inter 字体)
- Font Awesome (图标)

## 使用说明

1. 打开 `index.html` 文件即可使用。
2. 点击顶部导航栏中的分类链接，可以快速跳转到对应的内容区域。
3. 点击任意链接卡片，即可在新标签页中打开对应的网站。
4. 使用键盘的 `Tab` 键可以在链接卡片间导航，按 `Enter` 或 `Space` 键激活链接。
5. 滚动页面超过300px后，右下角会显示返回顶部按钮，点击可平滑滚动到页面顶部。

## 文件结构

```
.
├── index.html       # 主页面
├── style.css        # 样式文件
├── main.js          # 交互逻辑
└── README.md        # 项目说明文档
```

## 性能优化

### 简化的设计系统

为了确保在所有设备上都能流畅运行，我们对设计进行了优化：

- **简化的阴影系统**: 使用更少的阴影层，减少GPU渲染负担
- **优化的渐变背景**: 使用纯色背景替代复杂渐变，提高渲染性能
- **精简的动画效果**: 仅保留必要的过渡动画，避免复杂的关键帧动画

### 智能设备适配

- **自动性能检测**: 自动检测设备硬件能力，低端设备自动启用简化模式
- **动画自适应**: 在低性能设备上自动禁用所有动画效果
- **事件优化**: 使用节流和事件委托技术减少资源消耗

### 资源优化

- **减少HTTP请求**: 预加载关键资源，预连接外部域名
- **轻量级实现**: 移除了不必要的API调用（如Web Audio API）
- **代码压缩**: 优化JavaScript和CSS代码，减少文件大小

## 响应式设计

- 针对平板、手机等不同屏幕尺寸进行了优化，确保在各种设备上都有良好的用户体验。
- 返回顶部按钮在移动端自动调整大小和位置。
- 卡片网格布局根据屏幕尺寸自适应调整。

## 键盘支持

- 支持 `Tab` 键在链接卡片间导航。
- 支持 `Enter` 和 `Space` 键激活链接。
- 支持 `Enter` 和 `Space` 键激活导航链接。
- 支持 `Enter` 和 `Space` 键激活返回顶部按钮。
- 为使用键盘导航的用户提供清晰的焦点环。

## 自定义

- **颜色**: 可以通过修改 `:root` 中的 CSS 自定义属性来更改主题色。
- **字体**: 可以替换 Google Fonts 链接来使用其他字体。
- **布局**: 可以调整 `.link-grid` 的 `grid-template-columns` 来改变卡片的排列方式。

## 更新日志

### v2.0 - 性能优化版本
- 重构CSS样式系统，简化阴影和渐变效果
- 优化JavaScript代码，移除复杂API调用
- 增加低性能设备自动适配功能
- 简化动画效果，提高整体流畅度
- 减少文件大小，优化加载速度

### v1.0 - 初始版本
- 完整的粘土拟物化设计
- 机械键盘风格的交互效果
- 响应式布局设计
- 键盘导航支持